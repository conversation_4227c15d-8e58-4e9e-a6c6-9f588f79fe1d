// Static data for Mubwiza Garden website
export const categories = [
  {
    id: 1,
    name: 'Flowers',
    description: 'Beautiful fresh flowers for all occasions'
  },
  {
    id: 2,
    name: 'Vegetables',
    description: 'Fresh organic vegetables from our garden'
  },
  {
    id: 3,
    name: 'Fruits',
    description: 'Sweet and nutritious fruits'
  },
  {
    id: 4,
    name: 'Tea & Spices',
    description: 'Aromatic teas and natural spices'
  },
  {
    id: 5,
    name: 'Seedlings',
    description: 'Healthy seedlings for your garden'
  }
];

export const products = [
  // Flowers
  {
    id: 1,
    name: 'Beautiful Red Roses',
    description: 'Fresh red roses perfect for romantic occasions and special celebrations. Hand-picked from our garden.',
    price: 2500,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/red roses.jpeg',
    stock_quantity: 50,
    is_featured: true
  },
  {
    id: 2,
    name: 'Mixed Garden Flowers',
    description: 'A beautiful bouquet of mixed flowers from our garden. Perfect for brightening any space.',
    price: 3000,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flowers in garden in vase.jpeg',
    stock_quantity: 30,
    is_featured: false
  },
  {
    id: 15,
    name: 'Vibrant Red Roses',
    description: 'Stunning red roses that symbolize love and passion. Perfect for romantic occasions, anniversaries, or expressing deep emotions. These premium roses are carefully cultivated in our organic garden.',
    price: 4000,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 1.jpg',
    stock_quantity: 25,
    is_featured: true
  },
  {
    id: 16,
    name: 'Elegant White Lilies',
    description: 'Pure white lilies representing peace, rebirth, and tranquility. These graceful flowers are ideal for weddings, memorials, or creating a serene atmosphere in any space.',
    price: 3500,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 2.jpg',
    stock_quantity: 25,
    is_featured: false
  },
  {
    id: 17,
    name: 'Colorful Garden Mix',
    description: 'A delightful mixture of seasonal garden flowers featuring various colors and textures. This diverse bouquet brings joy and natural beauty to any environment.',
    price: 3800,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 3.jpg',
    stock_quantity: 20,
    is_featured: false
  },
  {
    id: 18,
    name: 'Sunflower Brilliance',
    description: 'Bright and cheerful sunflowers that radiate warmth and happiness. These magnificent flowers follow the sun and bring positive energy to any space, perfect for brightening someone\'s day.',
    price: 4200,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 4.jpg',
    stock_quantity: 20,
    is_featured: false
  },
  {
    id: 19,
    name: 'Tropical Paradise Blooms',
    description: 'Exotic tropical flowers with vibrant colors and unique shapes. These rare beauties bring a touch of paradise to your garden or home, featuring long-lasting blooms with exceptional fragrance.',
    price: 3600,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 5.jpg',
    stock_quantity: 30,
    is_featured: false
  },
  {
    id: 20,
    name: 'Spring Meadow Collection',
    description: 'A charming collection of spring wildflowers that captures the essence of a blooming meadow. These delicate flowers represent new beginnings and natural beauty.',
    price: 3900,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 6.jpg',
    stock_quantity: 25,
    is_featured: false
  },
  {
    id: 21,
    name: 'Premium Flower Collection 7',
    description: 'Handpicked flowers with natural beauty and fresh aroma.',
    price: 4100,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 7.jpg',
    stock_quantity: 20,
    is_featured: false
  },
  {
    id: 22,
    name: 'Premium Flower Collection 8',
    description: 'Exotic flowers that add elegance to any special occasion.',
    price: 4500,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 8.jpg',
    stock_quantity: 15,
    is_featured: true
  },
  {
    id: 23,
    name: 'Premium Flower Collection 9',
    description: 'Beautiful garden flowers with vibrant colors and lasting freshness.',
    price: 3700,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 9.jpg',
    stock_quantity: 25,
    is_featured: false
  },
  {
    id: 24,
    name: 'Premium Flower Collection 10',
    description: 'Stunning flower arrangement perfect for home and office decoration.',
    price: 4300,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 10.jpg',
    stock_quantity: 20,
    is_featured: false
  },
  {
    id: 25,
    name: 'Premium Flower Collection 11',
    description: 'Fresh seasonal flowers with exceptional beauty and quality.',
    price: 3800,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 11.jpg',
    stock_quantity: 25,
    is_featured: false
  },
  {
    id: 26,
    name: 'Premium Flower Collection 13',
    description: 'Elegant premium flowers perfect for special events and celebrations.',
    price: 4600,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 13.jpg',
    stock_quantity: 15,
    is_featured: true
  },
  {
    id: 3,
    name: 'Sunflower Bouquet',
    description: 'Bright and cheerful sunflowers that bring sunshine to your day.',
    price: 2800,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 1.jpg',
    stock_quantity: 25,
    is_featured: false
  },
  {
    id: 4,
    name: 'White Lilies',
    description: 'Elegant white lilies perfect for special occasions and ceremonies.',
    price: 3500,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 2.jpg',
    stock_quantity: 20,
    is_featured: false
  },
  {
    id: 5,
    name: 'Pink Carnations',
    description: 'Delicate pink carnations with a sweet fragrance.',
    price: 2200,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 3.jpg',
    stock_quantity: 35,
    is_featured: false
  },
  {
    id: 6,
    name: 'Purple Orchids',
    description: 'Exotic purple orchids that add elegance to any setting.',
    price: 4000,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 4.jpg',
    stock_quantity: 15,
    is_featured: true
  },
  {
    id: 7,
    name: 'Yellow Daffodils',
    description: 'Bright yellow daffodils that herald the arrival of spring.',
    price: 2000,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 5.jpg',
    stock_quantity: 40,
    is_featured: false
  },
  {
    id: 8,
    name: 'Blue Hydrangeas',
    description: 'Beautiful blue hydrangeas with full, round blooms.',
    price: 3200,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 6.jpg',
    stock_quantity: 18,
    is_featured: false
  },
  {
    id: 9,
    name: 'Orange Marigolds',
    description: 'Vibrant orange marigolds that bloom throughout the season.',
    price: 1800,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 7.jpg',
    stock_quantity: 45,
    is_featured: false
  },
  {
    id: 10,
    name: 'Pink Roses',
    description: 'Soft pink roses perfect for expressing gentle love and appreciation.',
    price: 2600,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 8.jpg',
    stock_quantity: 30,
    is_featured: false
  },
  {
    id: 11,
    name: 'White Chrysanthemums',
    description: 'Pure white chrysanthemums with intricate petal patterns.',
    price: 2400,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 9.jpg',
    stock_quantity: 25,
    is_featured: false
  },
  {
    id: 12,
    name: 'Mixed Tulips',
    description: 'Colorful tulips in various shades, perfect for spring arrangements.',
    price: 2900,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 10.jpg',
    stock_quantity: 22,
    is_featured: false
  },
  {
    id: 13,
    name: 'Lavender Bouquet',
    description: 'Fragrant lavender flowers with calming properties.',
    price: 2700,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 11.jpg',
    stock_quantity: 28,
    is_featured: false
  },
  {
    id: 14,
    name: 'Garden Daisy Mix',
    description: 'Cheerful daisies in white and yellow, perfect for casual bouquets.',
    price: 2100,
    category_id: 1,
    category_name: 'Flowers',
    image_url: '/images/flower 13.jpg',
    stock_quantity: 35,
    is_featured: false
  },

  // Vegetables
  {
    id: 15,
    name: 'Fresh Tomatoes',
    description: 'Juicy, ripe tomatoes grown organically in our garden. Perfect for salads and cooking.',
    price: 1500,
    category_id: 2,
    category_name: 'Vegetables',
    image_url: '/images/tomatoes.jpeg',
    stock_quantity: 100,
    is_featured: true
  },
  {
    id: 16,
    name: 'Garden Vegetables Mix',
    description: 'A fresh mix of seasonal vegetables including carrots, lettuce, and peppers.',
    price: 2000,
    category_id: 2,
    category_name: 'Vegetables',
    image_url: '/images/vegatebles in the garden.jpeg',
    stock_quantity: 75,
    is_featured: false
  },

  // Fruits
  {
    id: 17,
    name: 'Sweet Strawberries',
    description: 'Fresh, sweet strawberries picked at peak ripeness. Perfect for desserts or eating fresh.',
    price: 3000,
    category_id: 3,
    category_name: 'Fruits',
    image_url: '/images/strowberries.jpeg',
    stock_quantity: 60,
    is_featured: true
  },

  // Tea & Spices
  {
    id: 18,
    name: 'Fresh Mint Tea',
    description: 'Aromatic mint leaves perfect for brewing refreshing tea. Grown naturally in our herb garden.',
    price: 1200,
    category_id: 4,
    category_name: 'Tea & Spices',
    image_url: '/images/mint tea.jpeg',
    stock_quantity: 80,
    is_featured: false
  },
  {
    id: 19,
    name: 'Garden Spice Mix',
    description: 'A blend of fresh herbs and spices including basil, oregano, and thyme.',
    price: 1800,
    category_id: 4,
    category_name: 'Tea & Spices',
    image_url: '/images/tea spices.jpeg',
    stock_quantity: 50,
    is_featured: false
  },

  // Seedlings
  {
    id: 20,
    name: 'Tomato Seedlings',
    description: 'Healthy tomato seedlings ready for transplanting. Grow your own fresh tomatoes.',
    price: 500,
    category_id: 5,
    category_name: 'Seedlings',
    image_url: '/images/tomato seedling.jpeg',
    stock_quantity: 200,
    is_featured: false
  },
  {
    id: 21,
    name: 'Mixed Garden Seedlings',
    description: 'A variety of vegetable and herb seedlings perfect for starting your garden.',
    price: 800,
    category_id: 5,
    category_name: 'Seedlings',
    image_url: '/images/seedlings in the garden.jpeg',
    stock_quantity: 150,
    is_featured: true
  }
];

// Helper functions
export const getProductsByCategory = (categoryId) => {
  if (!categoryId) return products;
  return products.filter(product => product.category_id === parseInt(categoryId));
};

export const searchProducts = (searchTerm) => {
  if (!searchTerm) return products;
  const term = searchTerm.toLowerCase();
  return products.filter(product => 
    product.name.toLowerCase().includes(term) ||
    product.description.toLowerCase().includes(term) ||
    product.category_name.toLowerCase().includes(term)
  );
};

export const getProductById = (id) => {
  return products.find(product => product.id === parseInt(id));
};

export const getFeaturedProducts = () => {
  return products.filter(product => product.is_featured);
};
